// 认证相关功能

class AuthManager {
  constructor() {
    this.token = localStorage.getItem('token') || '';
    this.userInfo = this.getUserInfo();
    this.isLoading = false;
  }

  // 获取用户信息
  getUserInfo() {
    const savedUserInfo = localStorage.getItem('userInfo');
    if (savedUserInfo) {
      try {
        return JSON.parse(savedUserInfo);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        localStorage.removeItem('userInfo');
        return null;
      }
    }
    return null;
  }

  // 检查是否已认证
  isAuthenticated() {
    return !!this.token;
  }

  // 获取用户名
  getUsername() {
    return this.userInfo?.username || '';
  }

  // 登录
  async login(loginData) {
    try {
      this.isLoading = true;
      const response = await api.post('/auth/login', loginData);
      
      // 保存认证信息
      this.token = response.access_token;
      this.userInfo = response.user_info;
      
      // 保存到localStorage
      localStorage.setItem('token', response.access_token);
      localStorage.setItem('userInfo', JSON.stringify(response.user_info));
      
      showMessage('登录成功', 'success');
      return response;
    } catch (error) {
      showMessage(error.message || '登录失败', 'error');
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  // 登出
  async logout() {
    try {
      // 调用后端登出接口
      if (this.token) {
        await api.post('/auth/logout');
      }
    } catch (error) {
      console.error('登出接口调用失败:', error);
    } finally {
      // 清除本地状态
      this.token = '';
      this.userInfo = null;
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
      showMessage('已退出登录', 'success');
    }
  }

  // 获取当前用户信息
  async fetchUserInfo() {
    try {
      if (!this.token) return null;
      
      const response = await api.get('/auth/me');
      this.userInfo = response;
      localStorage.setItem('userInfo', JSON.stringify(response));
      return response;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 如果获取用户信息失败，可能token已过期
      if (error.message.includes('401')) {
        await this.logout();
      }
      throw error;
    }
  }

  // 验证Token
  async verifyToken() {
    try {
      if (!this.token) return false;
      
      const response = await api.get('/auth/verify');
      return response.success;
    } catch (error) {
      console.error('Token验证失败:', error);
      if (error.message.includes('401')) {
        await this.logout();
      }
      return false;
    }
  }

  // 检查认证状态
  async checkAuth() {
    if (!this.token) return false;
    
    try {
      const isValid = await this.verifyToken();
      if (!isValid) {
        await this.logout();
        return false;
      }
      
      // 如果没有用户信息，尝试获取
      if (!this.userInfo) {
        await this.fetchUserInfo();
      }
      
      return true;
    } catch (error) {
      console.error('检查认证状态失败:', error);
      await this.logout();
      return false;
    }
  }

  // 路由守卫
  async routeGuard(requiresAuth = true) {
    if (requiresAuth) {
      // 需要认证的页面
      if (!this.isAuthenticated()) {
        // 未登录，跳转到登录页
        window.location.href = 'login.html';
        return false;
      }

      // 已登录，验证token是否有效
      try {
        const isValid = await this.checkAuth();
        if (!isValid) {
          window.location.href = 'login.html';
          return false;
        }
      } catch (error) {
        console.error('认证检查失败:', error);
        window.location.href = 'login.html';
        return false;
      }
    } else {
      // 不需要认证的页面（如登录页）
      if (window.location.pathname.includes('login.html') && this.isAuthenticated()) {
        // 已登录用户访问登录页，重定向到首页
        window.location.href = 'reports.html';
        return false;
      }
    }

    return true;
  }
}

// 创建全局认证管理器实例
const authManager = new AuthManager();

// 页面加载时的认证检查
document.addEventListener('DOMContentLoaded', async () => {
  const currentPage = window.location.pathname;
  const isLoginPage = currentPage.includes('login.html');
  
  // 设置页面标题
  const pageTitle = getPageTitle(currentPage);
  if (pageTitle) {
    document.title = `${pageTitle} - 宿舍入住管理系统`;
  }

  // 执行路由守卫
  const canAccess = await authManager.routeGuard(!isLoginPage);
  if (!canAccess) {
    return;
  }

  // 如果是需要认证的页面，初始化用户信息显示
  if (!isLoginPage && authManager.isAuthenticated()) {
    updateUserInfo();
  }
});

// 获取页面标题
function getPageTitle(pathname) {
  const titleMap = {
    'login.html': '用户登录',
    'reports.html': '报表统计',
    'records.html': '入住记录',
    'departments.html': '部门管理',
    'dormitories.html': '宿舍管理',
    'residents.html': '住户管理',
    '404.html': '页面不存在'
  };

  for (const [page, title] of Object.entries(titleMap)) {
    if (pathname.includes(page)) {
      return title;
    }
  }

  return '宿舍入住管理系统';
}

// 更新用户信息显示
function updateUserInfo() {
  const usernameEl = document.querySelector('.username');
  if (usernameEl) {
    usernameEl.textContent = authManager.getUsername() || '用户';
  }
}

// 处理用户下拉菜单
function handleUserCommand(command) {
  switch (command) {
    case 'userInfo':
      showMessage('个人信息功能开发中...', 'info');
      break;
    case 'logout':
      handleLogout();
      break;
  }
}

// 处理登出
async function handleLogout() {
  const confirmed = await showConfirm('确定要退出登录吗？', '提示');
  if (confirmed) {
    try {
      await authManager.logout();
      window.location.href = 'login.html';
    } catch (error) {
      console.error('退出登录失败:', error);
    }
  }
}

// 导出到全局
window.authManager = authManager;
window.handleUserCommand = handleUserCommand;
window.handleLogout = handleLogout;
