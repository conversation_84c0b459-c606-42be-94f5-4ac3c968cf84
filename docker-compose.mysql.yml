version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: bedsharing-mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: dormitory_management
      MYSQL_USER: bedsharing
      MYSQL_PASSWORD: bedsharing123
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bedsharing-backend
    ports:
      - "8000:8000"
    volumes:
      - backend_logs:/app/logs
      - backend_exports:/app/exports
    environment:
      - DEBUG=false
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=bedsharing
      - MYSQL_PASSWORD=bedsharing123
      - MYSQL_DATABASE=dormitory_management
      - MYSQL_CHARSET=utf8mb4
      - SECRET_KEY=change-this-secret-key-in-production
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - CORS_ORIGINS=["http://localhost:3000"]
      - LOG_LEVEL=INFO
    depends_on:
      mysql:
        condition: service_healthy

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: bedsharing-frontend
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy

volumes:
  mysql_data:
    driver: local
  backend_logs:
    driver: local
  backend_exports:
    driver: local 