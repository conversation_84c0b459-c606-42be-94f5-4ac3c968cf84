#!/usr/bin/env python3
"""
MySQL连接测试脚本
"""
import os
import sys
import pymysql
from sqlalchemy import create_engine, text

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings


def test_direct_mysql_connection():
    """测试直接MySQL连接"""
    print("=== 测试直接MySQL连接 ===")
    
    try:
        connection = pymysql.connect(
            host=settings.mysql_host,
            port=settings.mysql_port,
            user=settings.mysql_user,
            password=settings.mysql_password,
            database=settings.mysql_database,
            charset=settings.mysql_charset
        )
        
        print(f"✅ 成功连接到MySQL数据库")
        print(f"   主机: {settings.mysql_host}:{settings.mysql_port}")
        print(f"   数据库: {settings.mysql_database}")
        print(f"   用户: {settings.mysql_user}")
        print(f"   字符集: {settings.mysql_charset}")
        
        # 测试查询
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"   MySQL版本: {version[0]}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接失败: {e}")
        return False


def test_sqlalchemy_connection():
    """测试SQLAlchemy连接"""
    print("\n=== 测试SQLAlchemy连接 ===")
    
    try:
        # 构建MySQL连接URL
        password_part = f":{settings.mysql_password}" if settings.mysql_password else ""
        database_url = f"mysql+pymysql://{settings.mysql_user}{password_part}@{settings.mysql_host}:{settings.mysql_port}/{settings.mysql_database}?charset={settings.mysql_charset}"
        
        # 创建引擎
        engine = create_engine(
            database_url,
            echo=True,  # 显示SQL语句
            pool_size=5,
            max_overflow=10,
            pool_pre_ping=True
        )
        
        # 测试连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1 as test"))
            row = result.fetchone()
            print(f"✅ SQLAlchemy连接成功，测试查询结果: {row[0]}")
        
        engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ SQLAlchemy连接失败: {e}")
        return False


def test_database_tables():
    """测试数据库表"""
    print("\n=== 测试数据库表 ===")
    
    try:
        connection = pymysql.connect(
            host=settings.mysql_host,
            port=settings.mysql_port,
            user=settings.mysql_user,
            password=settings.mysql_password,
            database=settings.mysql_database,
            charset=settings.mysql_charset
        )
        
        with connection.cursor() as cursor:
            # 查询所有表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            if tables:
                print(f"✅ 数据库中有 {len(tables)} 个表:")
                for table in tables:
                    print(f"   - {table[0]}")
            else:
                print("⚠️  数据库中没有表，可能需要运行数据库迁移")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 查询数据库表失败: {e}")
        return False


def main():
    """主函数"""
    print("MySQL连接测试工具")
    print("=" * 50)
    
    # 检查MySQL配置
    if not all([settings.mysql_host, settings.mysql_user, settings.mysql_database]):
        print("❌ 请先配置MySQL连接参数")
        print("请在.env文件中设置以下参数:")
        print("MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE")
        sys.exit(1)
    
    # 显示配置信息
    print(f"MySQL配置:")
    print(f"  主机: {settings.mysql_host}")
    print(f"  端口: {settings.mysql_port}")
    print(f"  用户: {settings.mysql_user}")
    print(f"  数据库: {settings.mysql_database}")
    print(f"  字符集: {settings.mysql_charset}")
    print()
    
    # 执行测试
    direct_ok = test_direct_mysql_connection()
    sqlalchemy_ok = test_sqlalchemy_connection()
    tables_ok = test_database_tables()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"  直接MySQL连接: {'✅ 成功' if direct_ok else '❌ 失败'}")
    print(f"  SQLAlchemy连接: {'✅ 成功' if sqlalchemy_ok else '❌ 失败'}")
    print(f"  数据库表检查: {'✅ 成功' if tables_ok else '❌ 失败'}")
    
    if all([direct_ok, sqlalchemy_ok]):
        print("\n🎉 MySQL配置正确，可以正常使用!")
    else:
        print("\n⚠️  请检查MySQL配置和连接")


if __name__ == "__main__":
    main() 