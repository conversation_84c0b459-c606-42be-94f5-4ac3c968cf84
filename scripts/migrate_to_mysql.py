#!/usr/bin/env python3
"""
SQLite到MySQL数据迁移脚本
"""
import os
import sys
import sqlite3
import pymysql
from typing import List, Dict, Any
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.config import settings


def get_sqlite_data(sqlite_path: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    从SQLite数据库读取所有数据
    
    Args:
        sqlite_path: SQLite数据库文件路径
        
    Returns:
        包含所有表数据的字典
    """
    if not os.path.exists(sqlite_path):
        print(f"错误: SQLite数据库文件不存在: {sqlite_path}")
        return {}
    
    conn = sqlite3.connect(sqlite_path)
    conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
    
    # 获取所有表名
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [row[0] for row in cursor.fetchall()]
    
    data = {}
    for table in tables:
        cursor.execute(f"SELECT * FROM {table}")
        rows = cursor.fetchall()
        table_data = []
        for row in rows:
            # 将sqlite3.Row转换为字典
            row_dict = dict(row)
            # 处理日期时间类型
            for key, value in row_dict.items():
                if isinstance(value, str) and 'T' in value:
                    try:
                        # 尝试解析ISO格式的日期时间
                        dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        row_dict[key] = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass
            table_data.append(row_dict)
        data[table] = table_data
        print(f"读取表 {table}: {len(table_data)} 条记录")
    
    conn.close()
    return data


def create_mysql_connection() -> pymysql.Connection:
    """
    创建MySQL连接
    
    Returns:
        MySQL连接对象
    """
    try:
        connection = pymysql.connect(
            host=settings.mysql_host,
            port=settings.mysql_port,
            user=settings.mysql_user,
            password=settings.mysql_password,
            database=settings.mysql_database,
            charset=settings.mysql_charset,
            autocommit=False
        )
        print(f"成功连接到MySQL数据库: {settings.mysql_host}:{settings.mysql_port}/{settings.mysql_database}")
        return connection
    except Exception as e:
        print(f"连接MySQL失败: {e}")
        sys.exit(1)


def migrate_data_to_mysql(data: Dict[str, List[Dict[str, Any]]], mysql_conn: pymysql.Connection):
    """
    将数据迁移到MySQL
    
    Args:
        data: 要迁移的数据
        mysql_conn: MySQL连接对象
    """
    cursor = mysql_conn.cursor()
    
    for table_name, table_data in data.items():
        if not table_data:
            continue
            
        print(f"迁移表 {table_name}: {len(table_data)} 条记录")
        
        for row in table_data:
            # 构建INSERT语句
            columns = list(row.keys())
            placeholders = ', '.join(['%s'] * len(columns))
            column_names = ', '.join([f'`{col}`' for col in columns])
            
            query = f"INSERT INTO `{table_name}` ({column_names}) VALUES ({placeholders})"
            values = list(row.values())
            
            try:
                cursor.execute(query, values)
            except Exception as e:
                print(f"插入数据失败 (表: {table_name}): {e}")
                print(f"SQL: {query}")
                print(f"数据: {values}")
                mysql_conn.rollback()
                return False
    
    mysql_conn.commit()
    print("数据迁移完成")
    return True


def main():
    """主函数"""
    print("=== SQLite到MySQL数据迁移工具 ===")
    
    # 检查MySQL配置
    if not all([settings.mysql_host, settings.mysql_user, settings.mysql_database]):
        print("错误: 请先配置MySQL连接参数")
        print("请在.env文件中设置以下参数:")
        print("MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE")
        sys.exit(1)
    
    # SQLite数据库路径
    sqlite_path = "dormitory_management.db"
    
    # 检查SQLite数据库是否存在
    if not os.path.exists(sqlite_path):
        print(f"错误: SQLite数据库文件不存在: {sqlite_path}")
        sys.exit(1)
    
    # 读取SQLite数据
    print("正在读取SQLite数据...")
    data = get_sqlite_data(sqlite_path)
    
    if not data:
        print("没有找到可迁移的数据")
        sys.exit(1)
    
    # 连接MySQL
    print("正在连接MySQL...")
    mysql_conn = create_mysql_connection()
    
    # 确认迁移
    total_records = sum(len(table_data) for table_data in data.values())
    print(f"\n准备迁移 {len(data)} 个表，共 {total_records} 条记录")
    
    confirm = input("确认开始迁移? (y/N): ").strip().lower()
    if confirm != 'y':
        print("迁移已取消")
        mysql_conn.close()
        sys.exit(0)
    
    # 执行迁移
    print("开始迁移数据...")
    success = migrate_data_to_mysql(data, mysql_conn)
    
    mysql_conn.close()
    
    if success:
        print("数据迁移成功完成!")
        print("请确保在.env文件中配置了MySQL参数，然后重启应用")
    else:
        print("数据迁移失败!")
        sys.exit(1)


if __name__ == "__main__":
    main() 