"""
数据库配置模块
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator
import pymysql

from .config import settings

def get_database_url() -> str:
    """
    获取数据库连接URL
    如果配置了MySQL参数，则使用MySQL连接
    否则使用配置的database_url
    """
    if all([
        settings.mysql_host,
        settings.mysql_user,
        settings.mysql_database
    ]):
        # 构建MySQL连接URL
        password_part = f":{settings.mysql_password}" if settings.mysql_password else ""
        return f"mysql+pymysql://{settings.mysql_user}{password_part}@{settings.mysql_host}:{settings.mysql_port}/{settings.mysql_database}?charset={settings.mysql_charset}"
    else:
        return settings.database_url

# 创建数据库引擎
database_url = get_database_url()
engine = create_engine(
    database_url,
    echo=settings.database_echo,
    # SQLite特定配置
    connect_args={"check_same_thread": False} if "sqlite" in database_url else {},
    # MySQL连接池配置
    pool_size=10 if "mysql" in database_url else None,
    max_overflow=20 if "mysql" in database_url else None,
    pool_pre_ping=True if "mysql" in database_url else None,
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    
    Yields:
        Session: 数据库会话
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """创建所有数据表"""
    # 确保所有模型都被导入
    from app.models import Department, Dormitory, Resident, ResidenceRecord, MonthlyReport, DailyAllocation
    from app.models.base import Base
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """删除所有数据表"""
    from app.models.base import Base
    Base.metadata.drop_all(bind=engine)
