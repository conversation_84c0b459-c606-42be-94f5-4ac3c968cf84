# 🏠 宿舍入住管理系统 (BedSharingCalc)

一个现代化的宿舍入住情况管理系统，支持入住记录管理、月度统计分析、部门费用分摊计算和数据导出功能。

## ✨ 功能特性

### 🏢 核心功能
- **入住记录管理**：完整的入住/离开记录管理，支持床位分配
- **月度统计分析**：按月统计床位使用情况和部门分摊比例
- **部门费用分摊**：基于床位天数的智能分摊算法
- **数据导出**：支持 Excel 格式的月度报告导出
- **宿舍管理**：多宿舍支持，灵活的床位配置

### 📊 报表功能
- **实时报告**：当月实时入住统计
- **月度报告**：历史月份详细统计
- **分布详情**：宿舍部门分布详细表格
- **数据可视化**：图表展示统计数据

### 🔐 系统特性
- **用户认证**：JWT + LDAP 双重认证支持
- **权限管理**：基于角色的访问控制
- **数据安全**：完整的数据验证和错误处理
- **响应式设计**：支持桌面和移动端访问

## 🛠️ 技术栈

### 后端技术
- **框架**：FastAPI 0.104.1
- **数据库**：SQLAlchemy 2.0.23 + SQLite/MySQL
- **认证**：JWT + LDAP3
- **数据处理**：Pandas + OpenPyXL
- **API文档**：自动生成 OpenAPI/Swagger 文档

### 前端技术
- **框架**：Vue 3.4.0 + Composition API
- **UI组件**：Element Plus 2.4.4
- **状态管理**：Pinia 2.1.7
- **路由**：Vue Router 4.2.5
- **图表**：ECharts 5.4.3
- **构建工具**：Vite 5.0.8

### 开发工具
- **容器化**：Docker + Docker Compose
- **代码质量**：Black + isort + Flake8
- **测试**：Pytest + 覆盖率测试
- **数据库迁移**：Alembic

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16.0+
- Docker & Docker Compose (可选)

### 1. 克隆项目
```bash
git clone <repository-url>
cd BedSharingCalc
```

### 2. 使用 Docker 启动 (推荐)
```bash
# SQLite版本 (默认)
docker-compose up -d

# MySQL版本
docker-compose -f docker-compose.mysql.yml up -d

# 开发环境
docker-compose -f docker-compose.dev.yml up -d

# 生产环境
docker-compose -f docker-compose.prod.yml up -d
```

### 3. 手动安装

#### 后端启动
```bash
# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python scripts/init_data.py

# 启动后端服务
python run.py
```

#### 前端启动
```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 4. 访问系统
- **前端界面**：http://localhost:3000
- **API文档**：http://localhost:8000/docs
- **API接口**：http://localhost:8000/api/v1

## 📁 项目结构

```
BedSharingCalc/
├── app/                    # 后端应用
│   ├── api/               # API路由
│   ├── auth/              # 认证模块
│   ├── core/              # 核心配置
│   ├── models/            # 数据模型
│   ├── repositories/      # 数据访问层
│   ├── schemas/           # 数据验证
│   ├── services/          # 业务逻辑
│   └── utils/             # 工具函数
├── frontend/              # 前端应用
│   ├── src/
│   │   ├── api/          # API调用
│   │   ├── components/   # 组件
│   │   ├── router/       # 路由配置
│   │   ├── stores/       # 状态管理
│   │   ├── utils/        # 工具函数
│   │   └── views/        # 页面组件
│   └── package.json
├── scripts/               # 脚本文件
├── tests/                 # 测试文件
├── 概要设计/               # 设计文档
├── docker-compose.yml     # Docker配置
├── requirements.txt       # Python依赖
└── README.md             # 项目文档
```

## 🔧 配置说明

### 环境变量
创建 `.env` 文件：
```env
# 应用配置
APP_NAME=宿舍入住管理系统
APP_VERSION=1.0.0
DEBUG=true

# 数据库配置 (SQLite - 默认)
DATABASE_URL=sqlite:///./dormitory_management.db

# MySQL配置 (可选 - 如果要使用MySQL)
# MYSQL_HOST=localhost
# MYSQL_PORT=3306
# MYSQL_USER=root
# MYSQL_PASSWORD=your_password
# MYSQL_DATABASE=dormitory_management
# MYSQL_CHARSET=utf8mb4

# JWT配置
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# LDAP配置 (可选)
LDAP_SERVER=ldap://your-ldap-server
LDAP_BASE_DN=dc=example,dc=com

# CORS配置
CORS_ORIGINS=["http://localhost:3000"]
```

### 数据库配置

#### SQLite (默认)
系统默认使用SQLite数据库，无需额外配置。

#### MySQL
如需使用MySQL数据库：

1. **安装MySQL依赖**：
   ```bash
   pip install pymysql cryptography
   ```

2. **创建MySQL数据库**：
   ```sql
   CREATE DATABASE dormitory_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

3. **配置环境变量**：
   在 `.env` 文件中添加MySQL配置参数。

4. **数据迁移**（如果从SQLite迁移）：
   ```bash
   python scripts/migrate_to_mysql.py
   ```

详细配置说明请参考 `MYSQL_SETUP.md`

### 数据库初始化
```bash
# 运行数据库迁移
alembic upgrade head

# 初始化基础数据
python scripts/init_data.py
```

## 📖 API 文档

系统提供完整的 RESTful API，主要接口包括：

### 认证接口
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌

### 数据管理
- `GET/POST/PUT/DELETE /api/v1/departments` - 部门管理
- `GET/POST/PUT/DELETE /api/v1/dormitories` - 宿舍管理
- `GET/POST/PUT/DELETE /api/v1/residents` - 住户管理
- `GET/POST/PUT/DELETE /api/v1/records` - 入住记录管理

### 报表接口
- `GET /api/v1/reports/realtime` - 实时报告
- `GET /api/v1/reports/monthly/{year}/{month}` - 月度报告
- `GET /api/v1/reports/monthly/{year}/{month}/distribution` - 分布详情
- `GET /api/v1/reports/monthly/{year}/{month}/export` - 导出Excel

详细的API文档可在 http://localhost:8000/docs 查看。

## 🧮 分摊算法

系统采用基于床位天数的分摊算法：

1. **床位天数计算**：每个住户的床位天数 = 在该月份的实际入住天数
2. **部门分摊比例**：部门分摊比例 = 该部门床位天数 / 总床位天数
3. **费用分摊**：各部门按分摊比例承担宿舍费用

详细算法设计请参考 `概要设计/2-分摊算法设计.md`

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=app --cov-report=html

# 运行特定测试
pytest tests/test_main.py
```

## 📦 部署

### Docker 部署
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 传统部署
1. 配置 Nginx 反向代理
2. 使用 Gunicorn 运行后端
3. 构建前端静态文件
4. 配置 SSL 证书

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 代码规范
- 后端：遵循 PEP 8，使用 Black 格式化
- 前端：遵循 Vue 3 风格指南，使用 Prettier 格式化
- 提交信息：使用约定式提交格式

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
1. 查看 [FAQ](docs/FAQ.md)
2. 提交 [Issue](issues)
3. 联系开发团队

## 🔄 更新日志

### v1.0.0 (2024-12-XX)
- ✨ 初始版本发布
- 🏠 完整的宿舍入住管理功能
- 📊 月度统计和报表功能
- 🔐 用户认证和权限管理
- 📱 响应式前端界面
- 🐳 Docker 容器化支持

## 🖼️ 系统截图

### 主要界面
- **登录页面**：简洁的用户认证界面
- **仪表板**：实时数据概览和快速操作
- **入住管理**：住户信息和入住记录管理
- **报表统计**：月度报告和数据可视化
- **宿舍管理**：宿舍和床位配置管理

### 核心功能展示
- **月度报告**：详细的统计数据和分摊比例
- **分布详情**：宿舍部门分布详细表格
- **数据导出**：Excel 格式的报表导出
- **响应式设计**：适配各种设备屏幕

## 🔍 系统架构

### 整体架构
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   前端界面   │────│   API网关   │────│   业务服务   │
│   Vue 3     │    │   FastAPI   │    │   Services  │
└─────────────┘    └─────────────┘    └─────────────┘
                           │                   │
                   ┌─────────────┐    ┌─────────────┐
                   │   认证服务   │    │   数据访问   │
                   │ JWT + LDAP  │    │ Repository  │
                   └─────────────┘    └─────────────┘
                                             │
                                    ┌─────────────┐
                                    │   数据库    │
                                    │ SQLite/PG   │
                                    └─────────────┘
```

### 数据流向
1. **用户操作** → 前端界面 (Vue 3)
2. **API请求** → 后端服务 (FastAPI)
3. **业务处理** → 服务层 (Services)
4. **数据操作** → 数据访问层 (Repository)
5. **数据存储** → 数据库 (SQLite/PostgreSQL)

## 📚 详细文档

### 设计文档
- [总体需求设计](概要设计/1-总体需求设计.md)
- [分摊算法设计](概要设计/2-分摊算法设计.md)
- [技术设计方案](概要设计/3-详细技术设计方案.md)
- [JWT鉴权系统设计](概要设计/JWT鉴权系统设计方案.md)
- [Excel格式设计](概要设计/月度报告Excel格式设计文档.md)

### 开发文档
- **API接口文档**：http://localhost:8000/docs
- **数据库设计**：查看 `app/models/` 目录
- **前端组件**：查看 `frontend/src/components/` 目录

## 🚨 常见问题

### 安装问题
**Q: 安装依赖时出现错误？**
A: 确保 Python 版本 >= 3.8，Node.js 版本 >= 16.0

**Q: 数据库连接失败？**
A: 检查 `.env` 文件中的数据库配置，确保数据库服务正常运行

### 使用问题
**Q: 登录失败？**
A: 检查用户名密码，或查看 LDAP 配置是否正确

**Q: 数据导出失败？**
A: 确保有足够的磁盘空间，检查文件权限

### 性能问题
**Q: 系统响应慢？**
A: 检查数据库索引，考虑升级硬件配置或优化查询

## 🔧 开发指南

### 本地开发环境搭建
1. **克隆代码**：`git clone <repo-url>`
2. **安装后端依赖**：`pip install -r requirements.txt`
3. **安装前端依赖**：`cd frontend && npm install`
4. **初始化数据库**：`python scripts/init_data.py`
5. **启动开发服务**：后端 `python run.py`，前端 `npm run dev`

### 代码提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 分支管理
- `main`: 主分支，稳定版本
- `develop`: 开发分支，最新功能
- `feature/*`: 功能分支
- `hotfix/*`: 紧急修复分支

## 🌟 特色功能

### 智能分摊算法
- 基于实际入住天数计算
- 支持跨月入住处理
- 自动处理入住状态变更

### 灵活的报表系统
- 实时数据统计
- 历史数据查询
- 多格式数据导出

### 现代化界面
- 响应式设计
- 暗色主题支持
- 移动端适配

### 企业级特性
- 完整的用户认证
- 细粒度权限控制
- 审计日志记录

---

**开发团队** | **技术支持** | **文档更新**

*最后更新：2024年12月*
