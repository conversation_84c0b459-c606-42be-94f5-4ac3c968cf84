# MySQL数据库配置说明

## 概述

本系统现在支持MySQL数据库。默认情况下使用SQLite，但可以通过环境变量配置切换到MySQL。

## 配置步骤

### 1. 安装MySQL依赖

系统已经添加了MySQL驱动依赖：
- `pymysql==1.1.0` - MySQL Python驱动
- `cryptography==41.0.7` - MySQL加密支持

### 2. 创建MySQL数据库

```sql
CREATE DATABASE dormitory_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 配置环境变量

创建 `.env` 文件并添加以下MySQL配置：

```bash
# MySQL配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=dormitory_management
MYSQL_CHARSET=utf8mb4
```

### 4. 数据库迁移

如果从SQLite迁移到MySQL，需要执行以下步骤：

1. 备份现有数据
2. 运行数据库迁移脚本
3. 验证数据完整性

### 5. 启动应用

配置完成后，正常启动应用即可使用MySQL数据库。

## 配置优先级

系统按以下优先级选择数据库：

1. 如果配置了完整的MySQL参数（host、user、database），则使用MySQL
2. 否则使用 `DATABASE_URL` 配置的数据库

## 注意事项

- MySQL连接使用连接池，提高性能
- 字符集使用 `utf8mb4` 支持完整的Unicode字符
- 建议在生产环境中使用强密码
- 确保MySQL服务已启动并可访问 