# MySQL快速配置指南

## 🚀 快速开始

### 方法一：使用Docker（推荐）

1. **启动MySQL版本**
   ```bash
   docker-compose -f docker-compose.mysql.yml up -d
   ```

2. **访问系统**
   - 前端：http://localhost:3000
   - API文档：http://localhost:8000/docs

### 方法二：手动配置

1. **安装MySQL依赖**
   ```bash
   pip install pymysql cryptography
   ```

2. **创建.env文件**
   ```bash
   # 复制环境变量模板
   cp .env.example .env
   
   # 编辑.env文件，添加MySQL配置
   MYSQL_HOST=localhost
   MYSQL_PORT=3306
   MYSQL_USER=root
   MYSQL_PASSWORD=your_password
   MYSQL_DATABASE=dormitory_management
   MYSQL_CHARSET=utf8mb4
   ```

3. **创建MySQL数据库**
   ```sql
   CREATE DATABASE dormitory_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

4. **测试连接**
   ```bash
   python scripts/test_mysql_connection.py
   ```

5. **数据迁移（如果需要）**
   ```bash
   python scripts/migrate_to_mysql.py
   ```

6. **启动应用**
   ```bash
   python run.py
   ```

## 🔧 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| MYSQL_HOST | localhost | MySQL主机地址 |
| MYSQL_PORT | 3306 | MySQL端口 |
| MYSQL_USER | root | MySQL用户名 |
| MYSQL_PASSWORD | | MySQL密码 |
| MYSQL_DATABASE | dormitory_management | 数据库名 |
| MYSQL_CHARSET | utf8mb4 | 字符集 |

### 配置优先级

1. 如果配置了完整的MySQL参数，系统使用MySQL
2. 否则使用 `DATABASE_URL` 配置的数据库（默认SQLite）

## 🐛 故障排除

### 常见问题

1. **连接失败**
   - 检查MySQL服务是否启动
   - 验证用户名密码是否正确
   - 确认数据库是否存在

2. **字符集问题**
   - 确保使用 `utf8mb4` 字符集
   - 检查数据库和表的字符集设置

3. **权限问题**
   - 确保用户有足够的数据库权限
   - 检查防火墙设置

### 测试工具

使用提供的测试脚本验证配置：
```bash
python scripts/test_mysql_connection.py
```

## 📚 更多信息

- 详细配置说明：`MYSQL_SETUP.md`
- 数据迁移工具：`scripts/migrate_to_mysql.py`
- 连接测试工具：`scripts/test_mysql_connection.py` 